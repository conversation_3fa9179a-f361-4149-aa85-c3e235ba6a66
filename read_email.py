#!/usr/bin/env python3
"""
Script para leer el último correo electrónico usando IMAP
"""
import imaplib
import email
import sys
from email.header import decode_header
import getpass

def decode_mime_words(s):
    """Decodifica palabras MIME en el header"""
    if s is None:
        return ""
    decoded_parts = decode_header(s)
    decoded_string = ""
    for part, encoding in decoded_parts:
        if isinstance(part, bytes):
            if encoding:
                decoded_string += part.decode(encoding)
            else:
                decoded_string += part.decode('utf-8', errors='ignore')
        else:
            decoded_string += part
    return decoded_string

def get_email_body(msg):
    """Extrae el cuerpo del correo"""
    body = ""
    if msg.is_multipart():
        for part in msg.walk():
            content_type = part.get_content_type()
            content_disposition = str(part.get("Content-Disposition"))
            
            if content_type == "text/plain" and "attachment" not in content_disposition:
                try:
                    body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    break
                except:
                    continue
    else:
        try:
            body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
        except:
            body = str(msg.get_payload())
    
    return body

def read_latest_email():
    """Lee el último correo electrónico"""
    
    # Solicitar credenciales
    print("=== Configuración IMAP ===")
    imap_server = input("Servidor IMAP (ej: imap.gmail.com): ").strip()
    if not imap_server:
        print("Error: Debes proporcionar el servidor IMAP")
        return
    
    port = input("Puerto IMAP (presiona Enter para 993): ").strip()
    port = int(port) if port else 993
    
    username = input("Usuario/Email: ").strip()
    if not username:
        print("Error: Debes proporcionar el usuario")
        return
    
    password = getpass.getpass("Contraseña: ")
    if not password:
        print("Error: Debes proporcionar la contraseña")
        return
    
    try:
        print(f"\nConectando a {imap_server}:{port}...")
        
        # Conectar al servidor IMAP
        mail = imaplib.IMAP4_SSL(imap_server, port)
        
        # Iniciar sesión
        print("Iniciando sesión...")
        mail.login(username, password)
        
        # Seleccionar la bandeja de entrada
        mail.select('INBOX')
        
        # Buscar todos los correos
        print("Buscando correos...")
        status, messages = mail.search(None, 'ALL')
        
        if status != 'OK':
            print("Error al buscar correos")
            return
        
        # Obtener la lista de IDs de correos
        email_ids = messages[0].split()
        
        if not email_ids:
            print("No se encontraron correos en la bandeja de entrada")
            return
        
        # Obtener el último correo (el ID más alto)
        latest_email_id = email_ids[-1]
        
        print(f"Obteniendo el último correo (ID: {latest_email_id.decode()})...")
        
        # Obtener el correo
        status, msg_data = mail.fetch(latest_email_id, '(RFC822)')
        
        if status != 'OK':
            print("Error al obtener el correo")
            return
        
        # Parsear el correo
        email_body = msg_data[0][1]
        email_message = email.message_from_bytes(email_body)
        
        # Mostrar información del correo
        print("\n" + "="*60)
        print("ÚLTIMO CORREO ELECTRÓNICO")
        print("="*60)
        
        # Decodificar y mostrar headers
        subject = decode_mime_words(email_message["Subject"])
        from_addr = decode_mime_words(email_message["From"])
        to_addr = decode_mime_words(email_message["To"])
        date = email_message["Date"]
        
        print(f"De: {from_addr}")
        print(f"Para: {to_addr}")
        print(f"Asunto: {subject}")
        print(f"Fecha: {date}")
        print("-" * 60)
        
        # Obtener y mostrar el cuerpo
        body = get_email_body(email_message)
        if body:
            print("CONTENIDO:")
            print(body[:2000])  # Mostrar solo los primeros 2000 caracteres
            if len(body) > 2000:
                print("\n[... contenido truncado ...]")
        else:
            print("No se pudo extraer el contenido del correo")
        
        print("="*60)
        
        # Cerrar conexión
        mail.close()
        mail.logout()
        print("\nConexión cerrada exitosamente")
        
    except imaplib.IMAP4.error as e:
        print(f"Error IMAP: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    read_latest_email()
