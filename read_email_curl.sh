#!/bin/bash
# Script para leer correos usando curl con IMAP

echo "=== Lector de Email con CURL ==="
echo

# Solicitar credenciales
read -p "Servidor IMAP (ej: imap.gmail.com): " IMAP_SERVER
read -p "Puerto (presiona Enter para 993): " PORT
PORT=${PORT:-993}

read -p "Usuario/Email: " USERNAME
read -s -p "Contraseña: " PASSWORD
echo

if [ -z "$IMAP_SERVER" ] || [ -z "$USERNAME" ] || [ -z "$PASSWORD" ]; then
    echo "Error: Todos los campos son obligatorios"
    exit 1
fi

echo
echo "Conectando a $IMAP_SERVER:$PORT..."

# Obtener lista de correos en INBOX
echo "Obteniendo lista de correos..."
EMAILS=$(curl -s --url "imaps://$IMAP_SERVER:$PORT/INBOX" \
    --user "$USERNAME:$PASSWORD" \
    --request "SEARCH ALL")

if [ $? -ne 0 ]; then
    echo "Error al conectar o buscar correos"
    exit 1
fi

echo "Lista de correos obtenida"

# Obtener el último correo (asumiendo que queremos el más reciente)
echo "Obteniendo el último correo..."
LATEST_EMAIL=$(curl -s --url "imaps://$IMAP_SERVER:$PORT/INBOX" \
    --user "$USERNAME:$PASSWORD" \
    --request "FETCH * (BODY[HEADER] BODY[TEXT])")

if [ $? -eq 0 ]; then
    echo
    echo "=========================================="
    echo "ÚLTIMO CORREO:"
    echo "=========================================="
    echo "$LATEST_EMAIL"
    echo "=========================================="
else
    echo "Error al obtener el correo"
    exit 1
fi
